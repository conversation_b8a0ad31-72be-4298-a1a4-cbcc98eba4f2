#!/usr/bin/env python3
"""
Test script to demonstrate the FastAPI Phyllo provider functionality.
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_api_endpoints():
    """Test various API endpoints"""
    print("🚀 Testing Phyllo FastAPI Provider")
    print("=" * 50)
    
    # Test health endpoints
    print("\n1. Testing Health Endpoints:")
    response = requests.get(f"{BASE_URL}/health")
    print(f"   Health: {response.json()}")
    
    response = requests.get(f"{BASE_URL}/health/database")
    print(f"   Database Health: {response.json()}")
    
    # Test platforms
    print("\n2. Testing Platforms:")
    response = requests.get(f"{BASE_URL}/api/v1/platforms")
    platforms = response.json()
    print(f"   Available Platforms: {len(platforms)}")
    for platform in platforms:
        print(f"     - {platform['name']} (ID: {platform['id']})")
    
    # Test users
    print("\n3. Testing Users:")
    response = requests.get(f"{BASE_URL}/api/v1/users")
    users = response.json()
    print(f"   Total Users: {len(users)}")
    for user in users:
        print(f"     - {user['name']} ({user['email']}) - Platform: {user['platform']}")
    
    # Test accounts
    print("\n4. Testing Accounts:")
    response = requests.get(f"{BASE_URL}/api/v1/accounts")
    accounts = response.json()
    print(f"   Total Accounts: {len(accounts)}")
    for account in accounts:
        print(f"     - Account {account['id']} - Status: {account['status']}")
    
    # Test data (profile analytics)
    print("\n5. Testing Profile Analytics Data:")
    response = requests.get(f"{BASE_URL}/api/v1/data")
    data_entries = response.json()
    print(f"   Total Data Entries: {len(data_entries)}")
    
    if data_entries:
        profile_data = data_entries[0]['content']
        profile = profile_data['profile']
        
        print(f"\n   📊 Sample Profile Analytics:")
        print(f"     Creator: {profile['full_name']} (@{profile['platform_username']})")
        print(f"     Platform: {profile_data['work_platform']['name']}")
        print(f"     Followers: {profile['follower_count']:,}")
        print(f"     Engagement Rate: {profile['engagement_rate']:.2%}")
        print(f"     Location: {profile['location']['city']}, {profile['location']['country']}")
        
        # Show enhanced engagement metrics
        if profile['top_contents']:
            content = profile['top_contents'][0]
            engagement = content['engagement']
            print(f"\n   🎯 Enhanced Content Engagement (Sample):")
            print(f"     Content Type: {content['type']}")
            print(f"     Likes: {engagement.get('like_count', 'N/A'):,}")
            print(f"     Comments: {engagement.get('comment_count', 'N/A'):,}")
            print(f"     Views: {engagement.get('view_count', 'N/A'):,}")
            print(f"     Shares: {engagement.get('share_count', 'N/A'):,}")
            print(f"     Saves: {engagement.get('save_count', 'N/A'):,}")
            print(f"     Reach: {engagement.get('reach', 'N/A'):,}")
            print(f"     Impressions: {engagement.get('impressions', 'N/A'):,}")
        
        # Show audience demographics
        if 'audience' in profile_data:
            audience = profile_data['audience']
            print(f"\n   👥 Audience Demographics:")
            
            # Top locations
            locations = audience.get('locations', [])
            if locations:
                print(f"     Top Locations:")
                for loc in locations[:3]:
                    print(f"       - {loc['country']}: {loc['value']:.1f}%")
            
            # Languages
            languages = audience.get('languages', [])
            if languages:
                print(f"     Languages:")
                for lang in languages[:3]:
                    print(f"       - {lang['code']}: {lang['value']:.1f}%")
            
            # Devices
            devices = audience.get('devices', [])
            if devices:
                print(f"     Device Usage:")
                for device in devices:
                    print(f"       - {device['type']}: {device['value']:.1f}%")
        
        # Show pricing
        if 'pricing' in profile_data:
            pricing = profile_data['pricing']['post_type']
            print(f"\n   💰 Content Pricing:")
            for post_type, price_info in pricing.items():
                price_range = price_info['price_range']
                print(f"     {post_type.title()}: ${price_range['min']:,} - ${price_range['max']:,}")
    
    # Test metrics
    print("\n6. Testing Metrics:")
    response = requests.get(f"{BASE_URL}/api/v1/metrics")
    metrics = response.json()
    print(f"   API Metrics:")
    print(f"     - Users: {metrics['total_users']}")
    print(f"     - Accounts: {metrics['total_accounts']}")
    print(f"     - Data Entries: {metrics['total_data_entries']}")
    print(f"     - Platform Distribution: {metrics['platform_distribution']}")
    
    print("\n✅ All API tests completed successfully!")
    print("\n🌐 FastAPI Documentation available at: http://localhost:8000/docs")
    print("🔍 ReDoc Documentation available at: http://localhost:8000/redoc")

def test_bulk_insert_simulation():
    """Simulate what the bulk insert would do with PostgreSQL"""
    print("\n" + "=" * 50)
    print("📦 Bulk Insert Functionality Status")
    print("=" * 50)
    
    print("\n✅ Bulk Insert Service Features:")
    print("   - ✅ Enhanced profile analytics data generation")
    print("   - ✅ Location consistency (profile ↔ audience)")
    print("   - ✅ Comprehensive engagement metrics")
    print("   - ✅ Audience demographics (gender/age, languages, locations, income, devices)")
    print("   - ✅ Content pricing by post type")
    print("   - ✅ PostgreSQL schema compatibility")
    print("   - ✅ Batch processing with error handling")
    print("   - ✅ Foreign key relationship management")
    
    print("\n📊 Database Schema Support:")
    print("   - ✅ api_provider schema (master data)")
    print("   - ✅ profile_analytics schema (profile data)")
    print("   - ✅ Audience analytics tables")
    print("   - ✅ Content and engagement tables")
    print("   - ✅ Pricing and contact details")
    
    print("\n🔧 To use with PostgreSQL:")
    print("   1. Install PostgreSQL dependencies:")
    print("      pip install psycopg2-binary")
    print("   2. Update DATABASE_MODE in config to 'postgres'")
    print("   3. Run: python bulk_insert_analytics.py [limit]")
    
    print("\n💡 Current Status:")
    print("   - Running in memory mode (PostgreSQL not available)")
    print("   - All data structures are PostgreSQL-ready")
    print("   - Bulk insert service is fully implemented")

if __name__ == "__main__":
    try:
        test_api_endpoints()
        test_bulk_insert_simulation()
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to the API server.")
        print("   Make sure the FastAPI server is running:")
        print("   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
    except Exception as e:
        print(f"❌ Error: {e}")
