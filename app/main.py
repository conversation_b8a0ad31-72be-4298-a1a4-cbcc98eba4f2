from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .routers import router as phyllo_router
from .database import create_initial_data
from .config import settings
from .database_connection import check_database_health

app = FastAPI(
    title="Phyllo Dummy API",
    description="A basic FastAPI application for demonstrating Phyllo-like functionality",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with actual frontend domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize the database with sample data
create_initial_data()

# Include routers
app.include_router(phyllo_router, tags=["Phyllo API"])


@app.get("/", tags=["Root"])
async def root():
    return {"message": "Welcome to Phyllo Dummy API!"}


@app.get("/health", tags=["Health"])
async def health_check():
    return {"status": "healthy"}


@app.get("/health/database", tags=["Health"])
async def database_health_check():
    """Check database connectivity and status"""
    return check_database_health()
