#!/usr/bin/env python3

import json
import random
import uuid
from datetime import datetime, timedelta
from faker import Faker
import string

# Initialize Faker
fake = Faker()

def generate_uuid():
    return str(uuid.uuid4())

def generate_image_url():
    return f"https://imgp.sptds.icu/v2?{fake.sha256()[:100]}"

def generate_platform():
    platforms = [
        {
            "id": "9bb8913b-ddd9-430b-a66a-d74d846e6c66",
            "name": "Instagram",
            "logo_url": "https://cdn.insightiq.ai/platforms_logo/logos/logo_instagram.png"
        },
        {
            "id": "8bb8913b-ddd9-430b-a66a-d74d846e6c67",
            "name": "YouTube",
            "logo_url": "https://cdn.insightiq.ai/platforms_logo/logos/logo_youtube.png"
        },
        {
            "id": "7bb8913b-ddd9-430b-a66a-d74d846e6c68",
            "name": "TikTok",
            "logo_url": "https://cdn.insightiq.ai/platforms_logo/logos/logo_tiktok.png"
        }
    ]
    return random.choice(platforms)

def generate_hashtag():
    tags = ["ad", "grwm", "fashion", "beauty", "lifestyle", "travel", "food", "fitness", "dance",
            "music", "photography", "art", "style", "makeup", "skincare", "trending"]
    return random.choice(tags)

def generate_hashtag_stats():
    tags = [
        "ad", "grwm", "fashion", "beauty", "lifestyle", "travel", 
        "dance", "music", "photography", "art", "style", "makeup",
        "food", "fitness", "family", "fun", "love", "happy", 
        "instagood", "photooftheday", "beautiful", "cute", "followme",
        "like4like", "follow4follow", "picoftheday", "selfie", "summer",
        "instadaily", "friends"
    ]
    sample_size = random.randint(10, min(len(tags), 20))
    return [
        {"name": tag, "value": round(random.uniform(2, 50), 4)}
        for tag in random.sample(tags, sample_size)
    ]

def generate_mentions():
    return [
        {
            "name": fake.user_name().lower(),
            "value": round(random.uniform(2, 15), 4)
        }
        for _ in range(random.randint(15, 25))
    ]

def generate_interests():
    interests = ["Beauty & Cosmetics", "Fashion", "Lifestyle", "Travel", "Food & Dining",
                "Fitness & Wellness", "Entertainment", "Technology", "Gaming", "Music"]
    return [{"name": interest} for interest in random.sample(interests, random.randint(1, 3))]

def generate_content():
    types = ["VIDEO", "IMAGE"]
    content_type = random.choice(types)
    username = fake.user_name()
    description = fake.text(max_nb_chars=100)
    
    return {
        "type": content_type,
        "url": f"https://www.instagram.com/p/{fake.bothify('?#?#?#?#?#??')}",
        "title": None if random.random() > 0.3 else fake.sentence(),
        "description": description,
        "thumbnail_url": generate_image_url(),
        "engagement": {"save_count": random.randint(100, 10000) if random.random() > 0.5 else None},
        "mentions": [] if random.random() > 0.7 else [username],
        "published_at": (datetime.now() - timedelta(days=random.randint(1, 365))).isoformat()
    }

def generate_reputation_history():
    current_followers = random.randint(1000000, 2000000)
    history = []
    for i in range(7):
        month = (datetime.now() + timedelta(days=30*i)).strftime("%Y-%m")
        history.append({
            "month": month,
            "follower_count": int(current_followers * (1 + random.uniform(-0.05, 0.05))),
            "subscriber_count": None,
            "following_count": random.randint(400, 800),
            "average_likes": random.randint(30000, 60000),
            "total_views": None,
            "average_views": None,
            "average_comments": None,
            "total_likes": None
        })
    return history

def generate_single_profile():
    platform = generate_platform()
    external_id = str(random.randint(1000000000, 9999999999))
    username = fake.user_name()
    full_name = fake.name()
    
    profile = {
        "id": generate_uuid(),
        "work_platform": platform,
        "profile": {
            "external_id": external_id,
            "platform_username": username,
            "url": f"https://www.instagram.com/{username}",
            "image_url": generate_image_url(),
            "full_name": full_name,
            "introduction": fake.text(max_nb_chars=50),
            "content_count": random.randint(500, 5000),
            "sponsored_posts_performance": round(random.uniform(0.1, 0.5), 6),
            "is_verified": random.random() > 0.7,
            "platform_account_type": "CREATOR",
            "gender": random.choice(["MALE", "FEMALE"]),
            "age_group": random.choice(["18-24", "25-34", "35-44"]),
            "language": "en",
            "follower_count": random.randint(100000, 2000000),
            "subscriber_count": None,
            "average_likes": random.randint(20000, 100000),
            "average_dislikes": None,
            "average_comments": random.randint(50, 500),
            "average_views": None,
            "average_reels_views": random.randint(200000, 1000000),
            "engagement_rate": round(random.uniform(0.01, 0.1), 6),
            "reputation_history": generate_reputation_history(),
            "location": {
                "city": fake.city(),
                "state": fake.state(),
                "country": fake.country()
            },
            "top_hashtags": generate_hashtag_stats(),
            "top_mentions": generate_mentions(),
            "top_interests": generate_interests(),
            "top_contents": [generate_content() for _ in range(15)],
            "recent_contents": [generate_content() for _ in range(10)]
        }
    }
    
    return profile

def generate_profiles(count=10000):
    profiles = []
    for i in range(count):
        if i > 0 and i % 100 == 0:
            print(f"Generated {i} profiles...")
        profiles.append(generate_single_profile())
    return profiles

if __name__ == "__main__":
    import sys
    count = 10000
    if len(sys.argv) > 1:
        try:
            count = int(sys.argv[1])
        except Exception:
            print("Invalid count argument, using default 10000")
    print(f"Generating {count} profile analytics records...")
    profiles = generate_profiles(count)
    
    output_file = "generated_profile_analytics.json"
    print(f"Writing profiles to {output_file}...")
    with open(output_file, "w") as f:
        json.dump(profiles, f, indent=2)
    print("Done!")
