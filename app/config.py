import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings:
    # API settings
    API_PORT: int = int(os.getenv("API_PORT", 8000))
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    
    # Phyllo API settings
    PHYLLO_API_VERSION: str = os.getenv("PHYLLO_API_VERSION", "v1")
    PHYLLO_ENVIRONMENT: str = os.getenv("PHYLLO_ENVIRONMENT", "sandbox")
    
    class Config:
        case_sensitive = True


settings = Settings()
