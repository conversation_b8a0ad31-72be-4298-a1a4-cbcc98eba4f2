"""
Database connection management for both PostgreSQL and in-memory databases.
Provides unified interface for database operations.
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from typing import Generator, Dict, Any, Optional
import logging
from .config import settings
from .database_models import Base

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database connections and sessions."""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self.memory_db = {
            "users": {},
            "accounts": {},
            "data": {}
        }
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database connection based on configuration."""
        if settings.DATABASE_MODE == "postgres":
            self._initialize_postgres()
        else:
            logger.info("Using in-memory database mode")
    
    def _initialize_postgres(self):
        """Initialize PostgreSQL connection."""
        try:
            self.engine = create_engine(
                settings.DATABASE_URL,
                poolclass=QueuePool,
                pool_size=settings.DATABASE_POOL_SIZE,
                max_overflow=settings.DATABASE_MAX_OVERFLOW,
                pool_timeout=settings.DATABASE_POOL_TIMEOUT,
                pool_recycle=settings.DATABASE_POOL_RECYCLE,
                echo=settings.DEBUG
            )
            
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # Test connection
            with self.engine.connect() as conn:
                conn.execute("SELECT 1")
            
            logger.info("PostgreSQL database connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL connection: {e}")
            logger.info("Falling back to in-memory database")
            self.engine = None
            self.SessionLocal = None
    
    @contextmanager
    def get_db_session(self) -> Generator[Session, None, None]:
        """Get database session context manager."""
        if self.SessionLocal is None:
            raise RuntimeError("PostgreSQL not available, use get_memory_db() instead")
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def get_memory_db(self) -> Dict[str, Any]:
        """Get in-memory database."""
        return self.memory_db
    
    def is_postgres_available(self) -> bool:
        """Check if PostgreSQL is available."""
        return self.engine is not None
    
    def get_engine(self):
        """Get SQLAlchemy engine."""
        return self.engine
    
    def create_tables(self):
        """Create all tables in the database."""
        if self.engine:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        else:
            logger.warning("Cannot create tables: PostgreSQL not available")

# Global database manager instance
db_manager = DatabaseManager()

# Dependency functions for FastAPI
def get_db_session():
    """FastAPI dependency for database session."""
    if db_manager.is_postgres_available():
        with db_manager.get_db_session() as session:
            yield session
    else:
        raise RuntimeError("PostgreSQL database not available")

def get_db():
    """FastAPI dependency for database (memory or postgres)."""
    if db_manager.is_postgres_available():
        with db_manager.get_db_session() as session:
            yield session
    else:
        yield db_manager.get_memory_db()

def get_memory_db():
    """FastAPI dependency for in-memory database."""
    return db_manager.get_memory_db()

# Database utility functions
def execute_raw_sql(sql: str, params: Optional[Dict] = None):
    """Execute raw SQL query."""
    if not db_manager.is_postgres_available():
        raise RuntimeError("Raw SQL execution requires PostgreSQL")
    
    with db_manager.get_db_session() as session:
        result = session.execute(sql, params or {})
        return result.fetchall()

def get_table_count(schema: str, table: str) -> int:
    """Get count of records in a table."""
    if not db_manager.is_postgres_available():
        return 0
    
    sql = f"SELECT COUNT(*) FROM {schema}.{table}"
    result = execute_raw_sql(sql)
    return result[0][0] if result else 0

def check_database_health() -> Dict[str, Any]:
    """Check database health and return status."""
    health_info = {
        "postgres_available": db_manager.is_postgres_available(),
        "database_mode": settings.DATABASE_MODE,
        "memory_db_stats": {}
    }
    
    if db_manager.is_postgres_available():
        try:
            with db_manager.get_db_session() as session:
                result = session.execute("SELECT version()")
                health_info["postgres_version"] = result.fetchone()[0]
                health_info["postgres_status"] = "healthy"
        except Exception as e:
            health_info["postgres_status"] = f"error: {str(e)}"
    
    # Memory database stats
    memory_db = db_manager.get_memory_db()
    health_info["memory_db_stats"] = {
        "users": len(memory_db.get("users", {})),
        "accounts": len(memory_db.get("accounts", {})),
        "data": len(memory_db.get("data", {}))
    }
    
    return health_info

# Initialize database on import
def initialize_database():
    """Initialize database and create tables if needed."""
    if db_manager.is_postgres_available():
        try:
            db_manager.create_tables()
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")

# Sample platforms data (for compatibility with existing code)
PLATFORMS = [
    {"id": "platform_1", "name": "YouTube", "logo": "youtube_logo.png"},
    {"id": "platform_2", "name": "Instagram", "logo": "instagram_logo.png"},
    {"id": "platform_3", "name": "TikTok", "logo": "tiktok_logo.png"},
    {"id": "platform_4", "name": "Twitter", "logo": "twitter_logo.png"},
    {"id": "platform_5", "name": "Twitch", "logo": "twitch_logo.png"},
]

def get_platforms():
    """Get supported platforms."""
    return PLATFORMS
